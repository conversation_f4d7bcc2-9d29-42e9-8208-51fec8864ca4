"use client";

import React from "react";
import Link from "next/link";

interface AuthLinkProps {
  regularText: string;
  linkText: string;
  href: string;
}

export const AuthLink: React.FC<AuthLinkProps> = ({ regularText, linkText, href }) => {
  return (
    <div className="mt-4">
      <span className="text-[14px] text-black" style={{ fontFamily: 'var(--font-open-sans)' }}>
        {regularText}{" "}
      </span>
      <Link href={href} className="auth-link text-[14px]" style={{ fontFamily: 'var(--font-open-sans)' }}>
        {linkText}
      </Link>
    </div>
  );
};
