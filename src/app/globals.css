@import "tailwindcss";

:root {
  --background: #FFFFFF;
  --foreground: #000000;
  --form-bg: #FFFFFF;
  --input-bg: #FFFFFF;
  --input-border: #E5E5E5;
  --input-placeholder: #9CA3AF;
  --button-primary-bg: #60A5FA;
  --button-primary-text: #FFFFFF;
  --button-secondary-bg: #F3F4F6;
  --button-secondary-text: #6B7280;
  --link-color: #60A5FA;
  --placeholder-bg: #D1D5DB;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-mina: var(--font-mina);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-mina), Arial, sans-serif;
}

.auth-form-container {
  background: var(--form-bg);
  border-radius: 0;
  padding: 2rem 1.5rem;
  box-shadow: none;
}

.auth-input {
  background: var(--input-bg);
  border: 1px solid var(--input-border);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  color: var(--foreground);
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-family: var(--font-mina);
  box-sizing: border-box;
  transition: border-color 0.2s ease;
}

.auth-input:focus {
  outline: none;
  border-color: var(--button-primary-bg);
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.1);
}

.auth-input::placeholder {
  color: var(--input-placeholder);
}

.auth-button-primary {
  background: var(--button-primary-bg);
  border-radius: 8px;
  color: var(--button-primary-text);
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  text-align: center;
  white-space: nowrap;
  border: none;
  cursor: pointer;
}

.auth-button-primary:hover {
  background: #3B82F6;
  transform: translateY(-1px);
}

.auth-button-secondary {
  background: var(--button-secondary-bg);
  border-radius: 8px;
  color: var(--button-secondary-text);
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  text-align: center;
  white-space: nowrap;
  border: 1px solid var(--input-border);
  cursor: pointer;
}

.auth-button-secondary:hover {
  background: #E5E7EB;
  transform: translateY(-1px);
}

.auth-link {
  color: var(--link-color);
  text-decoration: none;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-weight: 500;
}

.auth-link:hover {
  text-decoration: underline;
}

.auth-title {
  font-family: var(--font-mina);
  font-weight: 600;
  text-align: left;
  color: var(--foreground);
  text-shadow: none;
}

.placeholder-area {
  background: var(--placeholder-bg);
  border-radius: 8px;
}
