@import "tailwindcss";

:root {
  --background: #171717;
  --foreground: #FFFFFF;
  --form-bg: #071922;
  --input-bg: rgba(217, 217, 217, 0.57);
  --input-placeholder: rgba(217, 217, 217, 0.56);
  --button-bg: #DBD2CD;
  --button-text: #0D3245;
  --link-color: rgba(255, 255, 255, 0.54);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-mina: var(--font-mina);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-mina), Arial, sans-serif;
}

.auth-form-container {
  background: var(--form-bg);
  box-shadow: 8px 10px 10px rgba(0, 0, 0, 0.25);
  border-radius: 100px;
  padding: 2rem 1.5rem;
}

.auth-input {
  background: var(--input-bg);
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.52);
  border-radius: 100px;
  padding: 0.5rem 1.25rem;
  color: var(--foreground);
  width: 100%;
  height: 40px;
  font-size: 16px;
  font-family: var(--font-mina);
  box-sizing: border-box;
}

.auth-input::placeholder {
  color: var(--input-placeholder);
}

.auth-button {
  background: var(--button-bg);
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.52);
  border-radius: 100px;
  color: var(--button-text);
  font-weight: 700;
  padding: 0.5rem 1.5rem;
  transition: all 0.2s ease;
  text-align: center;
  white-space: nowrap;
}

.auth-button:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

.auth-link {
  color: var(--link-color);
  text-decoration: underline;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.auth-title {
  font-family: var(--font-mina);
  font-weight: 400;
  text-align: center;
  color: var(--foreground);
  text-shadow: 12px 4px 11px rgba(0, 0, 0, 0.25);
}
